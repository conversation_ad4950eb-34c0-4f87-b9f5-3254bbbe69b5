import express from 'express';
import cors from 'cors';
import jwt from 'jsonwebtoken';
import { initDatabase, authenticateUser, getAllUsers, getAllTasks, getTaskById, createTask, updateTask, deleteTask } from './database.js';

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = 'handyman-secret-key-demo-only';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://**************:5173'],
  credentials: true
}));
app.use(express.json());

// Initialize database on startup
initDatabase().catch(console.error);

// JWT middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Admin middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Routes
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    const user = await authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        full_name: user.full_name
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/profile', authenticateToken, (req, res) => {
  res.json({ user: req.user });
});

app.get('/api/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await getAllUsers();
    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Task management endpoints
app.get('/api/tasks', authenticateToken, async (req, res) => {
  try {
    const filters = {};

    // Apply role-based filtering
    if (req.user.role === 'employee') {
      // Employees can only see their assigned tasks or unassigned tasks
      filters.assigned_employee_id = req.user.id;
    }

    // Apply query filters
    if (req.query.status) {
      filters.status = req.query.status;
    }

    if (req.query.start_date) {
      filters.start_date = req.query.start_date;
    }

    if (req.query.end_date) {
      filters.end_date = req.query.end_date;
    }

    if (req.query.assigned_employee_id && req.user.role === 'admin') {
      filters.assigned_employee_id = req.query.assigned_employee_id;
    }

    const tasks = await getAllTasks(filters);
    res.json(tasks);
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/tasks/:id', authenticateToken, async (req, res) => {
  try {
    const task = await getTaskById(req.params.id);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check permissions
    if (req.user.role === 'employee' && task.assigned_employee_id !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json(task);
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/tasks', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      customer_name,
      customer_phone,
      customer_postcode,
      customer_email,
      service_type,
      service_description,
      start_date,
      estimated_end_date,
      service_fee,
      assigned_employee_id
    } = req.body;

    // Validate required fields
    if (!customer_name || !customer_phone || !customer_postcode || !service_type || !service_description || !start_date || !estimated_end_date) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const taskData = {
      customer_name,
      customer_phone,
      customer_postcode,
      customer_email,
      service_type,
      service_description,
      start_date,
      estimated_end_date,
      service_fee,
      assigned_employee_id,
      created_by: req.user.id,
      status: assigned_employee_id ? 'assigned' : 'pending'
    };

    const result = await createTask(taskData);
    const newTask = await getTaskById(result.id);

    res.status(201).json(newTask);
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/api/tasks/:id', authenticateToken, async (req, res) => {
  try {
    const task = await getTaskById(req.params.id);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check permissions
    if (req.user.role === 'employee') {
      // Employees can only update status and actual_end_date of their assigned tasks
      if (task.assigned_employee_id !== req.user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const allowedFields = ['status', 'actual_end_date'];
      const updateData = {};

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });

      const result = await updateTask(req.params.id, updateData);
      const updatedTask = await getTaskById(req.params.id);

      res.json(updatedTask);
    } else {
      // Admins can update any field
      const result = await updateTask(req.params.id, req.body);
      const updatedTask = await getTaskById(req.params.id);

      res.json(updatedTask);
    }
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/api/tasks/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await deleteTask(req.params.id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Handyman Management System API' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Local: http://localhost:${PORT}/api/health`);
  console.log(`Network: http://**************:${PORT}/api/health`);
});
