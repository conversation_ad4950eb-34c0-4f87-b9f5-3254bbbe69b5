import { useState, useEffect } from 'react'
import { getStoredAuth } from '../utils/auth'
import CreateTaskModal from './CreateTaskModal'

const TaskManagement = () => {
  const [tasks, setTasks] = useState([])
  const [employees, setEmployees] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    status: '',
    start_date: '',
    end_date: '',
    assigned_employee_id: ''
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedTask, setSelectedTask] = useState(null)
  const [showTaskDetail, setShowTaskDetail] = useState(false)

  useEffect(() => {
    fetchTasks()
    fetchEmployees()
  }, [filters])

  const fetchTasks = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const queryParams = new URLSearchParams()
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          queryParams.append(key, filters[key])
        }
      })

      const response = await fetch(`/api/tasks?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      } else {
        setError('Failed to fetch tasks')
      }
    } catch (err) {
      setError('Error fetching tasks')
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmployees(data.filter(user => user.role === 'employee'))
      }
    } catch (err) {
      console.error('Error fetching employees:', err)
    }
  }

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#ffc107'
      case 'assigned': return '#17a2b8'
      case 'in_progress': return '#007bff'
      case 'completed': return '#28a745'
      case 'cancelled': return '#dc3545'
      default: return '#6c757d'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  const formatCurrency = (amount) => {
    if (!amount) return '£0.00'
    return `£${parseFloat(amount).toFixed(2)}`
  }

  const handleTaskClick = (task) => {
    setSelectedTask(task)
    setShowTaskDetail(true)
  }

  const handleAssignTask = async (taskId, employeeId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          assigned_employee_id: employeeId,
          status: 'assigned'
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to assign task')
      }
    } catch (err) {
      setError('Error assigning task')
    }
  }

  const handleCompleteTask = async (taskId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          status: 'completed',
          actual_end_date: new Date().toISOString().split('T')[0]
        })
      })

      if (response.ok) {
        fetchTasks()
        setShowTaskDetail(false)
      } else {
        setError('Failed to complete task')
      }
    } catch (err) {
      setError('Error completing task')
    }
  }

  if (loading) {
    return <div className="loading">Loading tasks...</div>
  }

  return (
    <div className="task-management">
      {error && <div className="error-message">{error}</div>}
      
      {/* Task Header */}
      <div className="task-header">
        <h2>Task Management</h2>
        <button 
          className="action-button primary"
          onClick={() => setShowCreateModal(true)}
        >
          Create New Task
        </button>
      </div>

      {/* Task Filters */}
      <div className="task-filters">
        <div className="filter-group">
          <label>Status</label>
          <select 
            className="status-filter"
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="assigned">Assigned</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Start Date From</label>
          <input 
            type="date"
            className="date-filter"
            value={filters.start_date}
            onChange={(e) => handleFilterChange('start_date', e.target.value)}
          />
        </div>

        <div className="filter-group">
          <label>Start Date To</label>
          <input 
            type="date"
            className="date-filter"
            value={filters.end_date}
            onChange={(e) => handleFilterChange('end_date', e.target.value)}
          />
        </div>

        <div className="filter-group">
          <label>Assigned Employee</label>
          <select 
            className="employee-filter"
            value={filters.assigned_employee_id}
            onChange={(e) => handleFilterChange('assigned_employee_id', e.target.value)}
          >
            <option value="">All Employees</option>
            {employees.map(employee => (
              <option key={employee.id} value={employee.id}>
                {employee.full_name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Task List */}
      <div className="admin-tasks-container">
        {tasks.length === 0 ? (
          <div className="no-tasks">
            <p>No tasks found matching the current filters.</p>
          </div>
        ) : (
          tasks.map(task => (
            <div 
              key={task.id} 
              className={`admin-task-card ${task.status}`}
              onClick={() => handleTaskClick(task)}
            >
              <div className="task-header-info">
                <h4>{task.service_type} - {task.customer_name}</h4>
                <span 
                  className="task-status"
                  style={{ backgroundColor: getStatusColor(task.status) }}
                >
                  {task.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>

              <div className="task-details">
                <div className="customer-info">
                  <strong>Customer Details:</strong><br />
                  Name: {task.customer_name}<br />
                  Phone: {task.customer_phone}<br />
                  Postcode: {task.customer_postcode}<br />
                  {task.customer_email && `Email: ${task.customer_email}`}
                </div>

                <div className="service-info">
                  <strong>Service Details:</strong><br />
                  Type: {task.service_type}<br />
                  Description: {task.service_description}<br />
                  Fee: {formatCurrency(task.service_fee)}
                </div>

                <div className="assignment-info">
                  <strong>Schedule & Assignment:</strong><br />
                  Start: {formatDate(task.start_date)}<br />
                  Est. End: {formatDate(task.estimated_end_date)}<br />
                  {task.actual_end_date && `Actual End: ${formatDate(task.actual_end_date)}`}<br />
                  Assigned: {task.assigned_employee_name || 'Unassigned'}
                </div>
              </div>

              <div className="task-actions" onClick={(e) => e.stopPropagation()}>
                {task.status === 'pending' && (
                  <select 
                    className="btn-assign"
                    onChange={(e) => {
                      if (e.target.value) {
                        handleAssignTask(task.id, e.target.value)
                      }
                    }}
                    defaultValue=""
                  >
                    <option value="">Assign to...</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.full_name}
                      </option>
                    ))}
                  </select>
                )}
                
                {(task.status === 'in_progress' || task.status === 'assigned') && (
                  <button 
                    className="btn-view"
                    onClick={() => handleCompleteTask(task.id)}
                  >
                    Mark Complete
                  </button>
                )}
                
                <button className="btn-edit">Edit</button>
                <button className="btn-view">View Details</button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Task Detail Modal */}
      {showTaskDetail && selectedTask && (
        <TaskDetailModal 
          task={selectedTask}
          onClose={() => setShowTaskDetail(false)}
          onUpdate={fetchTasks}
        />
      )}

      {/* Create Task Modal */}
      {showCreateModal && (
        <CreateTaskModal 
          employees={employees}
          onClose={() => setShowCreateModal(false)}
          onSuccess={fetchTasks}
        />
      )}
    </div>
  )
}

// Task Detail Modal - placeholder for now
const TaskDetailModal = ({ task, onClose, onUpdate }) => {
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content task-detail-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Task Details - {task.service_type}</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <div className="task-detail-grid">
            <div className="detail-section">
              <h4>Customer Information</h4>
              <p><strong>Name:</strong> {task.customer_name}</p>
              <p><strong>Phone:</strong> {task.customer_phone}</p>
              <p><strong>Postcode:</strong> {task.customer_postcode}</p>
              {task.customer_email && <p><strong>Email:</strong> {task.customer_email}</p>}
            </div>

            <div className="detail-section">
              <h4>Service Details</h4>
              <p><strong>Type:</strong> {task.service_type}</p>
              <p><strong>Description:</strong> {task.service_description}</p>
              <p><strong>Fee:</strong> {task.service_fee ? `£${parseFloat(task.service_fee).toFixed(2)}` : 'Not set'}</p>
            </div>

            <div className="detail-section">
              <h4>Schedule & Assignment</h4>
              <p><strong>Start Date:</strong> {new Date(task.start_date).toLocaleDateString('en-GB')}</p>
              <p><strong>Est. End Date:</strong> {new Date(task.estimated_end_date).toLocaleDateString('en-GB')}</p>
              {task.actual_end_date && (
                <p><strong>Actual End:</strong> {new Date(task.actual_end_date).toLocaleDateString('en-GB')}</p>
              )}
              <p><strong>Assigned:</strong> {task.assigned_employee_name || 'Unassigned'}</p>
            </div>
          </div>

          <div className="timeline-section">
            <h4>Work Timeline</h4>
            <p>Timeline view and photo management coming soon...</p>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn-edit" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  )
}

export default TaskManagement
